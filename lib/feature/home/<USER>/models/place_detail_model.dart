class PlaceDetailModel {
  final int id;
  final int cityId;
  final int? serviceCategoryItemId;
  final String title;
  final String content;
  final String? image;
  final String? video;
  final double price;
  final double? weekendPrice;
  final double? weekPrice;
  final double? monthPrice;
  final double? lat;
  final double? lon;
  final int confirmation;
  final bool active;
  final int serviceCategoryId;
  final int? serviceCategoryFormId;
  final int userId;
  final int views;
  final String createdAt;
  final String updatedAt;
  final String? deletedAt;
  final double? rating;
  final int? noOfRates;
  final int? noGuests;
  final int? beds;
  final int? baths;
  final String? bookingRules;
  final String? cancelationRules;
  final int includeCommissionDaily;
  final int includeCommissionWeekly;
  final int includeCommissionMonthly;
  final bool favorite;
  final List<GalleryItemModel> gallery;
  final List<FacilityItemModel> facilities;
  final String country;
  final String city;
  final HosterModel hoster;

  const PlaceDetailModel({
    required this.id,
    required this.cityId,
    this.serviceCategoryItemId,
    required this.title,
    required this.content,
    this.image,
    this.video,
    required this.price,
    this.weekendPrice,
    this.weekPrice,
    this.monthPrice,
    this.lat,
    this.lon,
    required this.confirmation,
    required this.active,
    required this.serviceCategoryId,
    this.serviceCategoryFormId,
    required this.userId,
    required this.views,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    this.rating,
    this.noOfRates,
    this.noGuests,
    this.beds,
    this.baths,
    this.bookingRules,
    this.cancelationRules,
    required this.includeCommissionDaily,
    required this.includeCommissionWeekly,
    required this.includeCommissionMonthly,
    required this.favorite,
    required this.gallery,
    required this.facilities,
    required this.country,
    required this.city,
    required this.hoster,
  });

  factory PlaceDetailModel.fromJson(Map<String, dynamic> json) {
    return PlaceDetailModel(
      id: json['id'] ?? 0,
      cityId: json['city_id'] ?? 0,
      serviceCategoryItemId: json['service_category_item_id'],
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      image: json['image'],
      video: json['video'],
      price: double.tryParse(json['price']?.toString() ?? '0') ?? 0.0,
      weekendPrice: json['weekend_price'] != null 
          ? double.tryParse(json['weekend_price'].toString()) 
          : null,
      weekPrice: json['week_price'] != null 
          ? double.tryParse(json['week_price'].toString()) 
          : null,
      monthPrice: json['month_price'] != null 
          ? double.tryParse(json['month_price'].toString()) 
          : null,
      lat: json['lat'] != null 
          ? double.tryParse(json['lat'].toString()) 
          : null,
      lon: json['lon'] != null 
          ? double.tryParse(json['lon'].toString()) 
          : null,
      confirmation: json['confirmation'] ?? 0,
      active: json['active'] == 1 || json['active'] == true,
      serviceCategoryId: json['service_category_id'] ?? 0,
      serviceCategoryFormId: json['service_category_form_id'],
      userId: json['user_id'] ?? 0,
      views: json['views'] ?? 0,
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      deletedAt: json['deleted_at'],
      rating: json['rating'] != null 
          ? double.tryParse(json['rating'].toString()) 
          : null,
      noOfRates: json['no_of_rates'],
      noGuests: json['no_guests'],
      beds: json['beds'],
      baths: json['baths'],
      bookingRules: json['booking_rules'],
      cancelationRules: json['cancelation_rules'],
      includeCommissionDaily: json['include_commission_daily'] ?? 0,
      includeCommissionWeekly: json['include_commission_weekly'] ?? 0,
      includeCommissionMonthly: json['include_commission_monthly'] ?? 0,
      favorite: json['favorite'] ?? false,
      gallery: (json['gallery'] as List<dynamic>?)
          ?.map((item) => GalleryItemModel.fromJson(item))
          .toList() ?? [],
      facilities: (json['facilities'] as List<dynamic>?)
          ?.map((item) => FacilityItemModel.fromJson(item))
          .toList() ?? [],
      country: json['country'] ?? '',
      city: json['city'] ?? '',
      hoster: HosterModel.fromJson(json['hoster'] ?? {}),
    );
  }

  PlaceDetailModel copyWith({
    int? id,
    int? cityId,
    int? serviceCategoryItemId,
    String? title,
    String? content,
    String? image,
    String? video,
    double? price,
    double? weekendPrice,
    double? weekPrice,
    double? monthPrice,
    double? lat,
    double? lon,
    int? confirmation,
    bool? active,
    int? serviceCategoryId,
    int? serviceCategoryFormId,
    int? userId,
    int? views,
    String? createdAt,
    String? updatedAt,
    String? deletedAt,
    double? rating,
    int? noOfRates,
    int? noGuests,
    int? beds,
    int? baths,
    String? bookingRules,
    String? cancelationRules,
    int? includeCommissionDaily,
    int? includeCommissionWeekly,
    int? includeCommissionMonthly,
    bool? favorite,
    List<GalleryItemModel>? gallery,
    List<FacilityItemModel>? facilities,
    String? country,
    String? city,
    HosterModel? hoster,
  }) {
    return PlaceDetailModel(
      id: id ?? this.id,
      cityId: cityId ?? this.cityId,
      serviceCategoryItemId: serviceCategoryItemId ?? this.serviceCategoryItemId,
      title: title ?? this.title,
      content: content ?? this.content,
      image: image ?? this.image,
      video: video ?? this.video,
      price: price ?? this.price,
      weekendPrice: weekendPrice ?? this.weekendPrice,
      weekPrice: weekPrice ?? this.weekPrice,
      monthPrice: monthPrice ?? this.monthPrice,
      lat: lat ?? this.lat,
      lon: lon ?? this.lon,
      confirmation: confirmation ?? this.confirmation,
      active: active ?? this.active,
      serviceCategoryId: serviceCategoryId ?? this.serviceCategoryId,
      serviceCategoryFormId: serviceCategoryFormId ?? this.serviceCategoryFormId,
      userId: userId ?? this.userId,
      views: views ?? this.views,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      rating: rating ?? this.rating,
      noOfRates: noOfRates ?? this.noOfRates,
      noGuests: noGuests ?? this.noGuests,
      beds: beds ?? this.beds,
      baths: baths ?? this.baths,
      bookingRules: bookingRules ?? this.bookingRules,
      cancelationRules: cancelationRules ?? this.cancelationRules,
      includeCommissionDaily: includeCommissionDaily ?? this.includeCommissionDaily,
      includeCommissionWeekly: includeCommissionWeekly ?? this.includeCommissionWeekly,
      includeCommissionMonthly: includeCommissionMonthly ?? this.includeCommissionMonthly,
      favorite: favorite ?? this.favorite,
      gallery: gallery ?? this.gallery,
      facilities: facilities ?? this.facilities,
      country: country ?? this.country,
      city: city ?? this.city,
      hoster: hoster ?? this.hoster,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'city_id': cityId,
      'service_category_item_id': serviceCategoryItemId,
      'title': title,
      'content': content,
      'image': image,
      'video': video,
      'price': price,
      'weekend_price': weekendPrice,
      'week_price': weekPrice,
      'month_price': monthPrice,
      'lat': lat,
      'lon': lon,
      'confirmation': confirmation,
      'active': active,
      'service_category_id': serviceCategoryId,
      'service_category_form_id': serviceCategoryFormId,
      'user_id': userId,
      'views': views,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'deleted_at': deletedAt,
      'rating': rating,
      'no_of_rates': noOfRates,
      'no_guests': noGuests,
      'beds': beds,
      'baths': baths,
      'booking_rules': bookingRules,
      'cancelation_rules': cancelationRules,
      'include_commission_daily': includeCommissionDaily,
      'include_commission_weekly': includeCommissionWeekly,
      'include_commission_monthly': includeCommissionMonthly,
      'favorite': favorite,
      'gallery': gallery.map((item) => item.toJson()).toList(),
      'facilities': facilities.map((item) => item.toJson()).toList(),
      'country': country,
      'city': city,
      'hoster': hoster.toJson(),
    };
  }
}

class GalleryItemModel {
  final int id;
  final String image;
  final int cover;
  final String createdAt;

  const GalleryItemModel({
    required this.id,
    required this.image,
    required this.cover,
    required this.createdAt,
  });

  factory GalleryItemModel.fromJson(Map<String, dynamic> json) {
    return GalleryItemModel(
      id: json['id'] ?? 0,
      image: json['image'] ?? '',
      cover: json['cover'] ?? 0,
      createdAt: json['created_at'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'image': image,
      'cover': cover,
      'created_at': createdAt,
    };
  }
}

class FacilityItemModel {
  final int id;
  final String title;
  final String? icon;
  final int count;
  final String? createdAt;

  const FacilityItemModel({
    required this.id,
    required this.title,
    this.icon,
    required this.count,
    this.createdAt,
  });

  factory FacilityItemModel.fromJson(Map<String, dynamic> json) {
    return FacilityItemModel(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      icon: json['icon'],
      count: json['count'] ?? 0,
      createdAt: json['created_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'icon': icon,
      'count': count,
      'created_at': createdAt,
    };
  }
}

class HosterModel {
  final double? rating;
  final String name;
  final String registeredSince;
  final int? totalNoOfRates;

  const HosterModel({
    this.rating,
    required this.name,
    required this.registeredSince,
    this.totalNoOfRates,
  });

  factory HosterModel.fromJson(Map<String, dynamic> json) {
    return HosterModel(
      rating: json['rating'] != null 
          ? double.tryParse(json['rating'].toString()) 
          : null,
      name: json['name'] ?? '',
      registeredSince: json['registered_since'] ?? '',
      totalNoOfRates: json['total_no_of_rates'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'rating': rating,
      'name': name,
      'registered_since': registeredSince,
      'total_no_of_rates': totalNoOfRates,
    };
  }
}
