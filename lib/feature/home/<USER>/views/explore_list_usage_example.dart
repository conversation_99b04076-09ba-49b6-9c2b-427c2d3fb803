import 'package:flutter/material.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/feature/home/<USER>/views/explore_list_view.dart';
import 'package:gather_point/core/services/service_locator.dart';

/// Example usage of ExploreListScreen
class ExploreListUsageExample {
  
  /// Method 1: Navigate to explore list with category ID and title
  static void navigateToExploreList(
    BuildContext context, 
    int categoryId, 
    String categoryTitle
  ) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ExploreListScreen(
          categoryId: categoryId,
          categoryTitle: categoryTitle,
          dioConsumer: getIt<DioConsumer>(),
        ),
      ),
    );
  }

  /// Method 2: Navigate with custom DioConsumer
  static void navigateWithCustomDio(
    BuildContext context,
    int categoryId,
    String categoryTitle,
    DioConsumer dioConsumer,
  ) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ExploreListScreen(
          categoryId: categoryId,
          categoryTitle: categoryTitle,
          dioConsumer: dioConsumer,
        ),
      ),
    );
  }
}

/// Example widget showing how to use the ExploreListScreen
class ExampleCategoryListScreen extends StatelessWidget {
  final List<Map<String, dynamic>> categories = [
    {'id': 1, 'title': 'شاليهات', 'icon': Icons.house},
    {'id': 2, 'title': 'فلل', 'icon': Icons.villa},
    {'id': 3, 'title': 'شقق', 'icon': Icons.apartment},
    {'id': 4, 'title': 'استراحات', 'icon': Icons.weekend},
    {'id': 5, 'title': 'مخيمات', 'icon': Icons.camping},
  ];

  const ExampleCategoryListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختر الفئة'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: GridView.builder(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
          ),
          itemCount: categories.length,
          itemBuilder: (context, index) {
            final category = categories[index];
            return Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: () {
                  // Navigate to explore list for this category
                  ExploreListUsageExample.navigateToExploreList(
                    context,
                    category['id'],
                    category['title'],
                  );
                },
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Theme.of(context).colorScheme.primary.withOpacity(0.8),
                        Theme.of(context).colorScheme.primary,
                      ],
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        category['icon'],
                        size: 48,
                        color: Colors.white,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        category['title'],
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

/// Example of how to integrate with a home screen
class ExampleHomeIntegration extends StatelessWidget {
  const ExampleHomeIntegration({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('الرئيسية')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'استكشف الفئات',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            // Quick category buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      ExploreListUsageExample.navigateToExploreList(
                        context,
                        1,
                        'شاليهات',
                      );
                    },
                    icon: const Icon(Icons.house),
                    label: const Text('شاليهات'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      ExploreListUsageExample.navigateToExploreList(
                        context,
                        2,
                        'فلل',
                      );
                    },
                    icon: const Icon(Icons.villa),
                    label: const Text('فلل'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Featured section
            Card(
              child: ListTile(
                leading: const Icon(Icons.star, color: Colors.amber),
                title: const Text('الأكثر شعبية'),
                subtitle: const Text('اكتشف الأماكن الأكثر حجزاً'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  ExploreListUsageExample.navigateToExploreList(
                    context,
                    1, // Popular category ID
                    'الأكثر شعبية',
                  );
                },
              ),
            ),
            const SizedBox(height: 8),
            Card(
              child: ListTile(
                leading: const Icon(Icons.new_releases, color: Colors.green),
                title: const Text('الأحدث'),
                subtitle: const Text('اكتشف أحدث الإضافات'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  ExploreListUsageExample.navigateToExploreList(
                    context,
                    2, // New category ID
                    'الأحدث',
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
