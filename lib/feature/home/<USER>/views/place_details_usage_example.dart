import 'package:flutter/material.dart';
import 'package:gather_point/feature/home/<USER>/views/place_details_screen.dart';
import 'package:gather_point/feature/home/<USER>/models/place_detail_model.dart';

/// Example usage of PlaceDetailsScreen with different data sources
class PlaceDetailsUsageExample {
  
  /// Method 1: Using with API response data directly
  static void navigateWithApiResponse(BuildContext context, Map<String, dynamic> apiResponse) {
    // Extract the first item from API response
    final List<dynamic> data = apiResponse['data'] ?? [];
    if (data.isNotEmpty) {
      final placeDetail = PlaceDetailModel.fromJson(data.first);
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PlaceDetailsScreen(placeDetail: placeDetail),
        ),
      );
    }
  }

  /// Method 2: Using with place ID (will fetch data from API)
  static void navigateWithPlaceId(BuildContext context, int placeId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlaceDetailsScreen(placeId: placeId),
      ),
    );
  }

  /// Method 3: Using with legacy Map data (backward compatibility)
  static void navigateWithLegacyData(BuildContext context, Map<String, dynamic> placeData) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlaceDetailsScreen(placeData: placeData),
      ),
    );
  }

  /// Method 4: Using with pre-constructed PlaceDetailModel
  static void navigateWithPlaceDetailModel(BuildContext context, PlaceDetailModel placeDetail) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlaceDetailsScreen(placeDetail: placeDetail),
      ),
    );
  }

  /// Example of creating PlaceDetailModel from your API response
  static PlaceDetailModel createPlaceDetailFromApiResponse(Map<String, dynamic> apiItem) {
    return PlaceDetailModel.fromJson(apiItem);
  }

  /// Example of handling the API response structure you provided
  static List<PlaceDetailModel> parseApiResponse(Map<String, dynamic> apiResponse) {
    if (apiResponse['status'] == true && apiResponse['data'] != null) {
      final List<dynamic> data = apiResponse['data'];
      return data.map((item) => PlaceDetailModel.fromJson(item)).toList();
    }
    return [];
  }
}

/// Example widget showing how to use the PlaceDetailsScreen
class ExamplePlaceListScreen extends StatelessWidget {
  final Map<String, dynamic> sampleApiResponse = {
    "code": 200,
    "status": true,
    "message": "Data has been retrieved successfully",
    "data": [
      {
        "id": 1,
        "city_id": 1,
        "service_category_item_id": null,
        "title": "test reel video",
        "content": "this is dummy content",
        "image": "https://backend.gatherpoint.sa/storage/galleries/b90lwbju8j3zfmnwVMQDThxHxbMfIXnXfMEy4Yq8.jpg",
        "video": "https://backend.gatherpoint.sa/storage/service_category_items/OKemcqupeeIf2RDFqeyIdPRJkWaRpQIAMVb4uazY.mp4",
        "price": 500,
        "weekend_price": 600,
        "week_price": 0,
        "month_price": 0,
        "lat": null,
        "lon": null,
        "confirmation": 0,
        "active": 1,
        "service_category_id": 2,
        "service_category_form_id": null,
        "user_id": 1,
        "views": 513,
        "created_at": "2025-02-24T05:30:31.000000Z",
        "updated_at": "2025-06-15T11:01:39.000000Z",
        "deleted_at": null,
        "rating": null,
        "no_of_rates": null,
        "no_guests": null,
        "beds": null,
        "baths": null,
        "booking_rules": null,
        "cancelation_rules": null,
        "include_commission_daily": 0,
        "include_commission_weekly": 0,
        "include_commission_monthly": 0,
        "favorite": false,
        "gallery": [
          {
            "id": 1,
            "image": "https://backend.gatherpoint.sa/storage/galleries/b90lwbju8j3zfmnwVMQDThxHxbMfIXnXfMEy4Yq8.jpg",
            "cover": 1,
            "created_at": "2025-04-01T01:52:13.000000Z"
          }
        ],
        "facilities": [
          {
            "id": 1,
            "title": "wifi",
            "icon": "https://backend.gatherpoint.sa/storage/icons/67e9a074dfb25.png",
            "count": 0,
            "created_at": null
          }
        ],
        "country": "المملكة العربية السعودية",
        "city": "جدة",
        "hoster": {
          "rating": null,
          "name": "Gather Point",
          "registered_since": "2025-02-23T06:43:15.000000Z",
          "total_no_of_rates": null
        }
      }
    ]
  };

  const ExamplePlaceListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final places = PlaceDetailsUsageExample.parseApiResponse(sampleApiResponse);
    
    return Scaffold(
      appBar: AppBar(title: const Text('Places')),
      body: ListView.builder(
        itemCount: places.length,
        itemBuilder: (context, index) {
          final place = places[index];
          return ListTile(
            leading: Image.network(
              place.gallery.isNotEmpty 
                  ? place.gallery.first.image 
                  : place.image ?? '',
              width: 60,
              height: 60,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => 
                  const Icon(Icons.image_not_supported),
            ),
            title: Text(place.title),
            subtitle: Text('${place.city} - ${place.price.toStringAsFixed(0)} ر.س'),
            onTap: () {
              // Example: Navigate using the PlaceDetailModel directly
              PlaceDetailsUsageExample.navigateWithPlaceDetailModel(context, place);
              
              // Alternative: Navigate using place ID (will fetch from API)
              // PlaceDetailsUsageExample.navigateWithPlaceId(context, place.id);
            },
          );
        },
      ),
    );
  }
}
